# =====================================================
# Page Task Hub - Environment Configuration
# =====================================================
# Copy this file to .env.local and fill in your actual values
# Never commit .env.local to version control!

# =====================================================
# SUPABASE CONFIGURATION (Required)
# =====================================================
# Get these from your Supabase project dashboard:
# https://app.supabase.com/project/YOUR_PROJECT/settings/api

# Your Supabase project URL
VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Your Supabase anon/public key
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# =====================================================
# EMAIL SERVICE CONFIGURATION (Optional)
# =====================================================
# Configure email notifications for workspace invitations and task updates
# If not configured, the app will run in demo mode (emails logged to console)

# Email service provider: 'resend' | 'sendgrid' | 'mailgun'
# Leave empty or set to 'demo' to disable real email sending
VITE_EMAIL_SERVICE_PROVIDER=demo

# Your email service API key (required for real email sending)
# For Resend: Get from https://resend.com/api-keys
# For SendGrid: Get from https://app.sendgrid.com/settings/api_keys
VITE_EMAIL_SERVICE_API_KEY=

# Verified sender email address
# This email must be verified with your email service provider
VITE_FROM_EMAIL=<EMAIL>

# =====================================================
# DEVELOPMENT CONFIGURATION (Optional)
# =====================================================
# These are optional and have sensible defaults

# Development server port (default: 8080)
# VITE_PORT=8080

# Enable debug logging (default: false)
# VITE_DEBUG=false

# =====================================================
# SETUP INSTRUCTIONS
# =====================================================
# 1. Copy this file to .env.local
# 2. Create a Supabase project at https://supabase.com
# 3. Run the database schema from supabase-schema.sql
# 4. Run the RLS policies from supabase-rls-policies.sql
# 5. Run the functions and triggers from supabase-functions-triggers.sql
# 6. Enable Google OAuth in Supabase Auth settings
# 7. Fill in your VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY
# 8. (Optional) Configure email service for notifications
# 9. Run: npm install && npm run dev

# =====================================================
# TROUBLESHOOTING
# =====================================================
# - If you get "Missing Supabase environment variables" error:
#   Make sure your .env.local file is in the project root
#   and contains valid VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY
#
# - If authentication doesn't work:
#   Check that Google OAuth is enabled in Supabase
#   and the redirect URLs are configured correctly
#
# - If email notifications don't work:
#   Check your email service configuration
#   or set VITE_EMAIL_SERVICE_PROVIDER=demo for testing
#
# - For more help, see SUPABASE_SETUP_GUIDE.md
